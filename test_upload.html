<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>店小秘数据上传测试页面</title>
    <!-- 引入本地JSZip库用于创建ZIP文件 -->
    <script src="src/lib/jszip.min.js"></script>
    <!-- 引入主要的JavaScript文件 -->
    <script src="test_upload.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        textarea {
            width: 100%;
            height: 300px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        textarea:focus {
            border-color: #4CAF50;
            outline: none;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        button {
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #45a049;
        }
        
        .btn-secondary {
            background-color: #2196F3;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #1976D2;
        }
        
        .btn-warning {
            background-color: #FF9800;
            color: white;
        }
        
        .btn-warning:hover {
            background-color: #F57C00;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .response-container {
            margin-top: 20px;
            display: none;
        }

        .response-header {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            border: 1px solid #dee2e6;
            font-weight: bold;
            color: #495057;
        }

        .response-body {
            background-color: #ffffff;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 5px 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            word-break: break-all;
        }
        
        .info-box {
            background-color: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-box h3 {
            margin-top: 0;
            color: #1976D2;
        }
        
        .info-box ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .info-box li {
            margin: 5px 0;
        }
        
        .token-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .json-tools {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .json-tools button {
            padding: 5px 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 店小秘数据上传测试工具</h1>
        
        <div class="info-box">
            <h3>📋 使用说明</h3>
            <ul>
                <li><strong>目标API：</strong> https://www.dianxiaomi.com/api/popTemuProduct/add.json</li>
                <li><strong>处理流程：</strong> JSON → choiceSave.txt → ZIP压缩 → 上传</li>
                <li><strong>文件格式：</strong> form-data; name="file"; filename="blob"</li>
                <li><strong>参数：</strong> op=1, file=ZIP附件</li>
                <li><strong>认证：</strong> 自动获取店小秘网站的Cookie和Token</li>
                <li><strong>访问方式：</strong> 直接在浏览器中打开 test_upload.html 文件</li>
            </ul>
        </div>
        
        <div class="token-info">
            <strong>🔐 Token状态：</strong> <span id="tokenStatus">检测中...</span>
        </div>
        
        <div class="form-group">
            <label for="jsonData">JSON数据内容：</label>
            <div class="json-tools">
                <button type="button" class="btn-secondary" id="formatJsonBtn">格式化JSON</button>
                <button type="button" class="btn-secondary" id="compressJsonBtn">压缩JSON</button>
                <button type="button" class="btn-warning" id="loadSampleBtn">加载示例数据</button>
                <button type="button" class="btn-secondary" id="testZipBtn">测试ZIP创建</button>
            </div>
            <textarea id="jsonData" placeholder="请在此输入JSON数据..."></textarea>
        </div>
        
        <div class="button-group">
            <button type="button" class="btn-primary" id="uploadBtn">🚀 测试上传</button>
            <button type="button" class="btn-secondary" id="validateBtn">✅ 验证JSON</button>
            <button type="button" class="btn-warning" id="clearBtn">🗑️ 清空数据</button>
            <button type="button" class="btn-secondary" id="debugBtn">🔧 调试面板</button>
        </div>

        <div id="status" class="status"></div>

        <!-- 响应显示区域 -->
        <div id="responseContainer" class="response-container">
            <div class="response-header">
                📥 店小秘API响应结果
                <div style="float: right;">
                    <button type="button" id="copyResponseBtn" style="padding: 2px 8px; font-size: 12px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">复制</button>
                    <button type="button" id="hideResponseBtn" style="padding: 2px 8px; font-size: 12px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer; margin-left: 5px;">关闭</button>
                </div>
                <div style="clear: both;"></div>
            </div>
            <div id="responseBody" class="response-body"></div>
        </div>

        <!-- 调试面板 -->
        <div id="debugPanel" style="display: none; margin-top: 20px;">
            <div class="info-box">
                <h3>🔧 调试信息</h3>
                <div class="button-group" style="margin-bottom: 15px;">
                    <button type="button" class="btn-secondary" id="showCookiesBtn">查看Cookies</button>
                    <button type="button" class="btn-secondary" id="testCorsBtn">测试CORS</button>
                    <button type="button" class="btn-secondary" id="downloadBtn">下载为文件</button>
                </div>
                <div id="debugOutput" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
            </div>
        </div>
    </div>

    <script>
        // 检查店小秘Token状态
        function checkDxmToken() {
            const cookies = document.cookie;
            const hasDxmCookies = cookies.includes('dxm_') || cookies.includes('JSESSIONID');
            const tokenStatus = document.getElementById('tokenStatus');
            
            if (hasDxmCookies) {
                tokenStatus.textContent = '✅ 已检测到店小秘认证信息';
                tokenStatus.style.color = '#155724';
            } else {
                tokenStatus.textContent = '❌ 未检测到店小秘认证信息，请先登录店小秘网站';
                tokenStatus.style.color = '#721c24';
            }
        }
        
        // 加载示例数据
        function loadSampleData() {
            const sampleData = `{"attributes":"[{\\"propName\\":\\"是否可用于食品接触\\",\\"refPid\\":4010,\\"pid\\":1795,\\"templatePid\\":1261897,\\"numberInputValue\\":\\"\\",\\"valueUnit\\":\\"\\",\\"vid\\":\\"67313\\",\\"propValue\\":\\"是\\"}]","categoryId":"9938","shopId":"6959965","productSemiManagedReq":"100","sourceUrl":"https://www.amazon.com/dp/B0D2R2CGC1","productName":"3in1 Cup Lid Brush MultiFunction Gap Cleaning Brush","op":1}`;
            
            document.getElementById('jsonData').value = sampleData;
            showStatus('已加载示例数据', 'info');
        }
        
        // 格式化JSON
        function formatJson() {
            const textarea = document.getElementById('jsonData');
            try {
                const jsonObj = JSON.parse(textarea.value);
                textarea.value = JSON.stringify(jsonObj, null, 2);
                showStatus('JSON格式化成功', 'success');
            } catch (e) {
                showStatus('JSON格式错误：' + e.message, 'error');
            }
        }
        
        // 压缩JSON
        function compressJson() {
            const textarea = document.getElementById('jsonData');
            try {
                const jsonObj = JSON.parse(textarea.value);
                textarea.value = JSON.stringify(jsonObj);
                showStatus('JSON压缩成功', 'success');
            } catch (e) {
                showStatus('JSON格式错误：' + e.message, 'error');
            }
        }
        
        // 验证JSON
        function validateJson() {
            const jsonData = document.getElementById('jsonData').value.trim();
            
            if (!jsonData) {
                showStatus('请输入JSON数据', 'error');
                return false;
            }
            
            try {
                JSON.parse(jsonData);
                showStatus('✅ JSON格式验证通过', 'success');
                return true;
            } catch (e) {
                showStatus('❌ JSON格式错误：' + e.message, 'error');
                return false;
            }
        }
        
        // 测试ZIP文件创建
        async function testZipCreation() {
            const jsonData = document.getElementById('jsonData').value.trim();

            if (!validateJson()) {
                return;
            }

            try {
                showStatus('🧪 测试ZIP文件创建...', 'info');

                const zipBlob = await createZipFile(jsonData);

                showStatus(`✅ ZIP文件创建成功！\n原始大小: ${jsonData.length} bytes\nZIP大小: ${zipBlob.size} bytes\n压缩率: ${((1 - zipBlob.size / jsonData.length) * 100).toFixed(2)}%`, 'success');

                // 可选：自动下载测试文件
                if (confirm('ZIP文件创建成功！是否下载测试文件？')) {
                    const url = URL.createObjectURL(zipBlob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'test_choiceSave.zip';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }

            } catch (error) {
                showStatus(`❌ ZIP文件创建失败: ${error.message}`, 'error');
            }
        }

        // 清空数据
        function clearData() {
            document.getElementById('jsonData').value = '';
            hideStatus();
            hideResponse();
        }
        
        // 检查JSZip库是否加载
        function checkJSZipAvailability() {
            if (typeof JSZip === 'undefined') {
                debugLog('❌ JSZip库未加载');
                showStatus('❌ JSZip库未加载，请检查 src/lib/jszip.min.js 文件是否存在', 'error');
                return false;
            }
            debugLog('✅ JSZip库已加载');
            return true;
        }

        // 创建ZIP文件
        async function createZipFile(jsonData) {
            try {
                showStatus('📦 正在创建ZIP文件...', 'info');

                // 检查JSZip是否加载
                if (!checkJSZipAvailability()) {
                    throw new Error('JSZip库未加载，请确保 src/lib/jszip.min.js 文件存在');
                }

                // 创建ZIP实例
                const zip = new JSZip();

                // 将JSON数据添加到ZIP中作为choiceSave.txt
                zip.file('choiceSave.txt', jsonData);

                debugLog('正在生成ZIP文件...');

                // 生成ZIP文件
                const zipBlob = await zip.generateAsync({
                    type: 'blob',
                    compression: 'DEFLATE',
                    compressionOptions: {
                        level: 6
                    }
                });

                debugLog(`ZIP文件创建成功，大小: ${zipBlob.size} bytes`);
                return zipBlob;

            } catch (error) {
                console.error('创建ZIP文件失败:', error);
                debugLog(`❌ ZIP创建失败: ${error.message}`);
                throw new Error(`ZIP文件创建失败: ${error.message}`);
            }
        }

        // 上传数据
        async function uploadData() {
            const jsonData = document.getElementById('jsonData').value.trim();

            // 验证JSON
            if (!validateJson()) {
                return;
            }

            // 检查认证
            const cookies = document.cookie;
            if (!cookies.includes('dxm_') && !cookies.includes('JSESSIONID')) {
                showStatus('❌ 未检测到店小秘认证信息，请先登录店小秘网站', 'error');
                return;
            }

            try {
                // 第一步：创建ZIP文件
                const zipBlob = await createZipFile(jsonData);

                showStatus('🚀 正在上传ZIP文件...', 'info');

                // 第二步：创建FormData
                const formData = new FormData();

                // 添加ZIP文件，文件名为"blob"（按照curl命令的要求）
                formData.append('file', zipBlob, 'blob');
                formData.append('op', '1');

                // 记录请求详情
                console.log('📤 发送请求详情:');
                console.log('URL:', 'https://www.dianxiaomi.com/api/popTemuProduct/add.json');
                console.log('Method:', 'POST');
                console.log('FormData:', {
                    file: `blob (ZIP文件, ${zipBlob.size} bytes, 包含choiceSave.txt)`,
                    op: '1'
                });
                console.log('JSON原始大小:', jsonData.length, 'bytes');
                console.log('ZIP压缩后大小:', zipBlob.size, 'bytes');
                console.log('压缩率:', ((1 - zipBlob.size / jsonData.length) * 100).toFixed(2) + '%');
                console.log('Cookies:', document.cookie);

                // 发送请求
                const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include', // 包含cookies
                    headers: {
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'zh-CN,zh;q=0.9',
                        'Origin': 'https://www.dianxiaomi.com',
                        'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin',
                        'User-Agent': navigator.userAgent
                    }
                });

                console.log('📥 响应详情:');
                console.log('Status:', response.status, response.statusText);
                console.log('Headers:', Object.fromEntries(response.headers.entries()));

                const result = await response.text();
                console.log('Response Body:', result);

                // 显示响应结果
                showResponse(response, result);

                if (response.ok) {
                    let displayResult = result;
                    try {
                        // 尝试格式化JSON响应
                        const jsonResult = JSON.parse(result);
                        displayResult = JSON.stringify(jsonResult, null, 2);
                    } catch (e) {
                        // 如果不是JSON，保持原样
                    }

                    showStatus(`✅ 上传成功！\n响应状态: ${response.status} ${response.statusText}`, 'success');
                } else {
                    showStatus(`❌ 上传失败！\n状态码: ${response.status} ${response.statusText}`, 'error');
                }

            } catch (error) {
                console.error('❌ Upload error:', error);

                if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                    showStatus(`❌ 网络错误: 无法连接到服务器\n可能原因:\n1. 网络连接问题\n2. CORS跨域限制\n3. 服务器不可达\n\n建议: 请确保在店小秘网站域名下使用此工具`, 'error');
                } else {
                    showStatus(`❌ 上传出错: ${error.message}`, 'error');
                }
            }
        }

        // 获取详细的Cookie信息
        function getCookieDetails() {
            const cookies = document.cookie.split(';');
            const cookieObj = {};

            cookies.forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name) {
                    cookieObj[name] = value || '';
                }
            });

            return cookieObj;
        }

        // 显示Cookie详情
        function showCookieDetails() {
            const cookies = getCookieDetails();
            const dxmCookies = Object.keys(cookies).filter(key =>
                key.includes('dxm_') || key.includes('JSESSIONID') || key.includes('MYJ_')
            );

            if (dxmCookies.length > 0) {
                console.log('🍪 检测到的店小秘相关Cookies:');
                dxmCookies.forEach(key => {
                    console.log(`${key}: ${cookies[key]}`);
                });
            } else {
                console.log('❌ 未检测到店小秘相关Cookies');
            }
        }
        
        // 显示响应结果
        function showResponse(response, responseBody) {
            const container = document.getElementById('responseContainer');
            const bodyDiv = document.getElementById('responseBody');

            // 构建响应信息
            let responseInfo = '';
            responseInfo += `状态码: ${response.status} ${response.statusText}\n`;
            responseInfo += `请求时间: ${new Date().toLocaleString()}\n`;
            responseInfo += `响应大小: ${responseBody.length} 字节\n\n`;

            // 响应头信息
            responseInfo += '=== 响应头 ===\n';
            for (const [key, value] of response.headers.entries()) {
                responseInfo += `${key}: ${value}\n`;
            }
            responseInfo += '\n';

            // 响应体
            responseInfo += '=== 响应体 ===\n';
            try {
                // 尝试格式化JSON
                const jsonData = JSON.parse(responseBody);
                responseInfo += JSON.stringify(jsonData, null, 2);
            } catch (e) {
                // 如果不是JSON，直接显示原文
                responseInfo += responseBody;
            }

            bodyDiv.textContent = responseInfo;
            container.style.display = 'block';

            // 滚动到响应区域
            container.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示状态信息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';

            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    hideStatus();
                }, 8000);
            }
        }

        // 隐藏状态信息
        function hideStatus() {
            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'none';
        }

        // 隐藏响应信息
        function hideResponse() {
            const container = document.getElementById('responseContainer');
            container.style.display = 'none';
        }

        // 复制响应内容
        async function copyResponse() {
            const responseBody = document.getElementById('responseBody');
            const text = responseBody.textContent;

            try {
                await navigator.clipboard.writeText(text);
                showStatus('✅ 响应内容已复制到剪贴板', 'success');
            } catch (err) {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('✅ 响应内容已复制到剪贴板', 'success');
            }
        }
        
        // 切换调试面板
        function toggleDebugPanel() {
            const panel = document.getElementById('debugPanel');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                debugLog('调试面板已打开');
            } else {
                panel.style.display = 'none';
            }
        }

        // 调试日志
        function debugLog(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        // 显示Cookie详情（增强版）
        function showCookieDetails() {
            const cookies = getCookieDetails();
            const dxmCookies = Object.keys(cookies).filter(key =>
                key.includes('dxm_') || key.includes('JSESSIONID') || key.includes('MYJ_')
            );

            debugLog('=== Cookie 详情 ===');
            debugLog(`总Cookie数量: ${Object.keys(cookies).length}`);
            debugLog(`店小秘相关Cookie数量: ${dxmCookies.length}`);

            if (dxmCookies.length > 0) {
                debugLog('店小秘相关Cookies:');
                dxmCookies.forEach(key => {
                    const value = cookies[key];
                    const displayValue = value.length > 50 ? value.substring(0, 50) + '...' : value;
                    debugLog(`  ${key}: ${displayValue}`);
                });
            } else {
                debugLog('❌ 未检测到店小秘相关Cookies');
                debugLog('请确保已登录店小秘网站');
            }

            debugLog('当前域名: ' + window.location.hostname);
            debugLog('当前协议: ' + window.location.protocol);
        }

        // 测试CORS
        async function testCORS() {
            debugLog('=== CORS 测试 ===');
            debugLog('测试目标: https://www.dianxiaomi.com');

            try {
                const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
                    method: 'OPTIONS',
                    credentials: 'include'
                });

                debugLog(`OPTIONS请求状态: ${response.status}`);
                debugLog('CORS Headers:');

                const corsHeaders = ['access-control-allow-origin', 'access-control-allow-methods', 'access-control-allow-headers'];
                corsHeaders.forEach(header => {
                    const value = response.headers.get(header);
                    debugLog(`  ${header}: ${value || '未设置'}`);
                });

            } catch (error) {
                debugLog(`CORS测试失败: ${error.message}`);

                if (window.location.hostname !== 'www.dianxiaomi.com') {
                    debugLog('⚠️ 当前不在店小秘域名下，可能存在CORS限制');
                    debugLog('建议: 将此页面部署到店小秘网站或使用浏览器扩展');
                }
            }
        }

        // 下载为文件（支持ZIP和TXT两种格式）
        async function downloadAsFile() {
            const jsonData = document.getElementById('jsonData').value.trim();

            if (!jsonData) {
                showStatus('请先输入JSON数据', 'error');
                return;
            }

            try {
                // 验证JSON
                JSON.parse(jsonData);

                // 询问用户下载格式
                const downloadZip = confirm('选择下载格式:\n确定 = ZIP文件（与上传格式一致）\n取消 = TXT文件');

                if (downloadZip) {
                    // 下载ZIP格式
                    const zipBlob = await createZipFile(jsonData);
                    const url = URL.createObjectURL(zipBlob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'choiceSave.zip';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    debugLog('ZIP文件下载成功: choiceSave.zip');
                    showStatus('ZIP文件下载成功', 'success');
                } else {
                    // 下载TXT格式
                    const blob = new Blob([jsonData], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'choiceSave.txt';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    debugLog('TXT文件下载成功: choiceSave.txt');
                    showStatus('TXT文件下载成功', 'success');
                }

            } catch (e) {
                showStatus('文件创建失败: ' + e.message, 'error');
            }
        }

        // 初始化事件监听器
        function initEventListeners() {
            // JSON工具按钮
            document.getElementById('formatJsonBtn').addEventListener('click', formatJson);
            document.getElementById('compressJsonBtn').addEventListener('click', compressJson);
            document.getElementById('loadSampleBtn').addEventListener('click', loadSampleData);
            document.getElementById('testZipBtn').addEventListener('click', testZipCreation);

            // 主要功能按钮
            document.getElementById('uploadBtn').addEventListener('click', uploadData);
            document.getElementById('validateBtn').addEventListener('click', validateJson);
            document.getElementById('clearBtn').addEventListener('click', clearData);
            document.getElementById('debugBtn').addEventListener('click', toggleDebugPanel);

            // 调试面板按钮
            document.getElementById('showCookiesBtn').addEventListener('click', showCookieDetails);
            document.getElementById('testCorsBtn').addEventListener('click', testCORS);
            document.getElementById('downloadBtn').addEventListener('click', downloadAsFile);

            // 响应区域按钮
            document.getElementById('copyResponseBtn').addEventListener('click', copyResponse);
            document.getElementById('hideResponseBtn').addEventListener('click', hideResponse);
        }

        // 页面加载时检查Token状态
        window.addEventListener('load', function() {
            // 初始化事件监听器
            initEventListeners();

            // 检查JSZip库
            checkJSZipAvailability();

            checkDxmToken();
            debugLog('页面加载完成');
            debugLog('当前URL: ' + window.location.href);

            // 定期检查Token状态
            setInterval(checkDxmToken, 5000);
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'Enter':
                        e.preventDefault();
                        uploadData();
                        break;
                    case 'l':
                        e.preventDefault();
                        loadSampleData();
                        break;
                    case 'f':
                        e.preventDefault();
                        formatJson();
                        break;
                }
            }
        });
    </script>
</body>
</html>
